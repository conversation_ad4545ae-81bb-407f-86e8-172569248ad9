---
title: Models & Pricing
description: 'Available AI models, costs, and capabilities'
---

## Overview

Cubent Coder provides access to 23+ cutting-edge AI models from leading providers including Anthropic, OpenAI, Google, DeepSeek, and xAI. Each model is optimized for different use cases, from quick code suggestions to complex architectural decisions.

All models use our unified **Cubent Units** system for transparent, predictable pricing across providers.

## Available Models

### 🤖 **Anthropic Claude Models**

<table>
  <thead>
    <tr>
      <th>Model</th>
      <th>Cubent Units</th>
      <th>Free</th>
      <th>Pro</th>
      <th>Growth</th>
      <th>Teams</th>
      <th>Enterprise</th>
      <th>Images</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Claude 3.7 Sonnet</td>
      <td>1.1</td>
      <td>–</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
    </tr>
    <tr>
      <td>Claude 3.7 Sonnet (Thinking)</td>
      <td>1.35</td>
      <td>–</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
    </tr>
    <tr>
      <td>Claude 3.5 Sonnet</td>
      <td>0.95</td>
      <td>–</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
    </tr>
    <tr>
      <td>Claude 3.5 Haiku</td>
      <td>0.55</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>–</td>
    </tr>
    <tr>
      <td>Claude 3 Haiku</td>
      <td>0.45</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>–</td>
    </tr>
  </tbody>
</table>

### 🧠 **OpenAI Models**

<table>
  <thead>
    <tr>
      <th>Model</th>
      <th>Cubent Units</th>
      <th>Free</th>
      <th>Pro</th>
      <th>Growth</th>
      <th>Teams</th>
      <th>Enterprise</th>
      <th>Images</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>GPT-4o</td>
      <td>1.1</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
    </tr>
    <tr>
      <td>GPT-4.5 Preview</td>
      <td>1.2</td>
      <td>–</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
    </tr>
    <tr>
      <td>GPT-4o Mini</td>
      <td>0.65</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>–</td>
    </tr>
    <tr>
      <td>O3 Mini</td>
      <td>1.0</td>
      <td>–</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>–</td>
    </tr>
    <tr>
      <td>O3 Mini (High Reasoning)</td>
      <td>1.1</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
    </tr>
    <tr>
      <td>O3 Mini (Low Reasoning)</td>
      <td>0.75</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>–</td>
    </tr>
  </tbody>
</table>

### 🔍 **DeepSeek Models**

<table>
  <thead>
    <tr>
      <th>Model</th>
      <th>Cubent Units</th>
      <th>Free</th>
      <th>Pro</th>
      <th>Growth</th>
      <th>Teams</th>
      <th>Enterprise</th>
      <th>Images</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>DeepSeek Chat</td>
      <td>0.35</td>
      <td>–</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>–</td>
    </tr>
    <tr>
      <td>DeepSeek Reasoner</td>
      <td>0.7</td>
      <td>–</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>–</td>
    </tr>
  </tbody>
</table>

### 💎 **Google Gemini Models**

<table>
  <thead>
    <tr>
      <th>Model</th>
      <th>Cubent Units</th>
      <th>Free</th>
      <th>Pro</th>
      <th>Growth</th>
      <th>Teams</th>
      <th>Enterprise</th>
      <th>Images</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Gemini 2.5 Flash</td>
      <td>0.3</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
    </tr>
    <tr>
      <td>Gemini 2.5 Flash (Thinking)</td>
      <td>0.4</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
    </tr>
    <tr>
      <td>Gemini 2.5 Pro</td>
      <td>0.85</td>
      <td>–</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
    </tr>
    <tr>
      <td>Gemini 2.0 Flash</td>
      <td>0.45</td>
      <td>–</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
    </tr>
    <tr>
      <td>Gemini 2.0 Pro</td>
      <td>0.70</td>
      <td>–</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
    </tr>
    <tr>
      <td>Gemini 1.5 Flash</td>
      <td>0.40</td>
      <td>–</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
    </tr>
    <tr>
      <td>Gemini 1.5 Pro</td>
      <td>0.65</td>
      <td>–</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
    </tr>
  </tbody>
</table>

### 🚀 **xAI Grok Models**

<table>
  <thead>
    <tr>
      <th>Model</th>
      <th>Cubent Units</th>
      <th>Free</th>
      <th>Pro</th>
      <th>Growth</th>
      <th>Teams</th>
      <th>Enterprise</th>
      <th>Images</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Grok 3</td>
      <td>1.1</td>
      <td>–</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>No*</td>
    </tr>
    <tr>
      <td>Grok 3 Mini</td>
      <td>0.30</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>No*</td>
    </tr>
    <tr>
      <td>Grok 2 Vision</td>
      <td>0.70</td>
      <td>–</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
      <td>✓</td>
    </tr>
  </tbody>
</table>

*Grok 3 currently supports text modality only. Vision capabilities coming soon according to xAI.

## BYOK Models (Bring Your API Key)

Access the latest models using your own API keys:

### 🤖 **Anthropic BYOK**
- **Claude Sonnet 4** - Latest flagship model with vision
- **Claude Sonnet 4 (Thinking)** - Advanced reasoning capabilities  
- **Claude 4 Opus** - Most capable model for complex tasks
- **Claude 4 Opus (Thinking)** - Advanced reasoning + maximum capability
- **Claude 3.7 Sonnet (Thinking)** - Advanced reasoning for complex problems
- **Claude 3.5 Sonnet** - Balanced performance and speed

### 💎 **Google BYOK**
- **Gemini 2.5 Pro** - Advanced reasoning and multimodal capabilities

### 🚀 **xAI BYOK**
- **Grok 3** - Latest xAI model (text-only, vision coming soon)
- **Grok 2 Vision** - Multimodal capabilities with vision support

## Understanding Cubent Units

Cubent Units provide a unified way to measure AI usage across different models:

- **Lower cost models** (like Gemini 2.5 Flash) use fewer units
- **Premium models** (like Claude 3.7 Sonnet Thinking) use more units
- **Thinking models** use additional units for reasoning capabilities
- **Image processing** may use additional units depending on the model

## Model Capabilities

### 🖼️ **Vision Support**
Models marked with ✓ in the Images column can:
- Analyze screenshots and images
- Read text from images (OCR)
- Understand diagrams and charts
- Process visual content in code reviews

### 🧠 **Reasoning Models**
"Thinking" models provide:
- Step-by-step problem solving
- Complex logical reasoning
- Detailed analysis and planning
- Enhanced code architecture decisions

### ⚡ **Speed vs Quality**
- **Fast models** (Haiku, Mini): Quick responses for simple tasks
- **Balanced models** (Sonnet, GPT-4o): Good balance of speed and quality
- **Premium models** (Opus, Thinking): Maximum quality for complex tasks

## Choosing the Right Model

### For Beginners
Start with **Gemini 2.5 Flash** or **Claude 3.5 Haiku** - they're efficient and available on the Free plan.

### For General Development
**Claude 3.5 Sonnet** or **GPT-4o** provide excellent balance of capability and cost.

### For Complex Tasks
Use **Claude 3.7 Sonnet (Thinking)** or **O3 Mini (High Reasoning)** for complex architecture decisions.

### For Image Analysis
**GPT-4o**, **Claude 3.7 Sonnet**, or **Gemini 2.5 Pro** offer excellent vision capabilities.

### For Cost Optimization
**DeepSeek Chat** (0.35 units) offers great value, while **Gemini 2.5 Flash** (0.3 units) is the most economical.

## Getting Started

<CardGroup cols={2}>
  <Card
    title="Start Free"
    icon="rocket"
    href="https://cubent.dev/signup"
  >
    Begin with our Free plan and 8 available models
  </Card>
  <Card
    title="Compare Plans"
    icon="chart-bar"
    href="https://cubent.dev/pricing"
  >
    See detailed pricing and feature comparison
  </Card>
</CardGroup>

## Frequently Asked Questions

### What happens when I run out of Cubent Units?
You can upgrade your plan, wait for the monthly reset, or switch to BYOK models with your own API keys.

### Can I mix built-in and BYOK models?
Yes! You can use both built-in models (with Cubent Units) and BYOK models (with your API keys) in the same workspace.

### How do thinking models work?
Thinking models show their reasoning process, providing step-by-step analysis before giving their final answer.

### Are there any usage limits?
Each plan has monthly Cubent Unit allocations. Enterprise plans can have custom limits based on your needs.

### Can I change plans anytime?
Yes, you can upgrade or downgrade your plan at any time. Changes take effect at the next billing cycle.
