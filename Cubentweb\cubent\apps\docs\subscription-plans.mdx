---
title: Subscription Plans
description: 'Choose the perfect plan for your AI-powered coding needs'
---

## Overview

Cubent Coder offers flexible subscription plans to match your development needs. From individual developers to large enterprises, we have a plan that provides the right balance of features, models, and support.

## Subscription Plans

### 🆓 **Free Plan**
Perfect for getting started with AI-powered coding.

- **Monthly Cubent Units**: Limited allocation
- **Available Models**: 8 models including GPT-4o, Gemini 2.5 Flash, Claude 3.5 Haiku
- **Features**: Chat Mode, basic file operations
- **Support**: Community support

### 💎 **Pro Plan**
Ideal for individual developers and freelancers.

- **Monthly Cubent Units**: Generous allocation
- **Available Models**: All 23 built-in models
- **Features**: Chat Mode, Agent Mode, Custom Modes, Terminal Integration
- **Support**: Priority community support

### 🚀 **Growth Plan**
Perfect for growing teams and small businesses.

- **Monthly Cubent Units**: Higher allocation
- **Available Models**: All 23 built-in models
- **Features**: All Pro features + team collaboration tools
- **Support**: Email support + priority community

### 👥 **Teams Plan**
Designed for development teams and organizations.

- **Monthly Cubent Units**: Team-sized allocation
- **Available Models**: All 23 built-in models
- **Features**: All Growth features + team management, usage analytics
- **Support**: Priority email support + dedicated Slack channel

### 🏢 **Enterprise Plan**
For large organizations with custom requirements.

- **Monthly Cubent Units**: Custom allocation
- **Available Models**: All models + custom model integration
- **Features**: All features + SSO, custom deployment, SLA
- **Support**: Dedicated account manager + phone support

### 🔑 **BYOK (Bring Your API Key)**
Use your own API keys with any plan.

- **Cost**: No additional charges from Cubent
- **Billing**: Direct billing from AI providers
- **Models**: Access to latest models as they're released
- **Control**: Full control over usage and costs

## Understanding Cubent Units

Cubent Units provide a unified way to measure AI usage across different models:

- **Lower cost models** (like Gemini 2.5 Flash) use fewer units
- **Premium models** (like Claude 3.7 Sonnet Thinking) use more units
- **Thinking models** use additional units for reasoning capabilities
- **Image processing** may use additional units depending on the model

## Plan Comparison

<CardGroup cols={3}>
  <Card
    title="Free Plan"
    icon="gift"
    href="https://cubent.dev/signup"
  >
    Start coding with AI for free - 8 models included
  </Card>
  <Card
    title="Pro Plan"
    icon="star"
    href="https://cubent.dev/pricing"
  >
    Full access to all 23 models and advanced features
  </Card>
  <Card
    title="Enterprise"
    icon="building"
    href="https://cubent.dev/contact"
  >
    Custom solutions for large organizations
  </Card>
</CardGroup>

## Frequently Asked Questions

### What happens when I run out of Cubent Units?
You can upgrade your plan, wait for the monthly reset, or switch to BYOK models with your own API keys.

### Can I mix built-in and BYOK models?
Yes! You can use both built-in models (with Cubent Units) and BYOK models (with your API keys) in the same workspace.

### Can I change plans anytime?
Yes, you can upgrade or downgrade your plan at any time. Changes take effect at the next billing cycle.

### Are there any usage limits?
Each plan has monthly Cubent Unit allocations. Enterprise plans can have custom limits based on your needs.

## Getting Started

Ready to supercharge your coding with AI? Choose your plan and start building better software faster.

<CardGroup cols={2}>
  <Card
    title="Start Free"
    icon="rocket"
    href="https://cubent.dev/signup"
  >
    Begin with our Free plan and 8 available models
  </Card>
  <Card
    title="View All Plans"
    icon="chart-bar"
    href="https://cubent.dev/pricing"
  >
    Compare features and pricing for all plans
  </Card>
</CardGroup>
